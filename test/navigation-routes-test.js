/**
 * Test script to validate all navigation routes are working correctly
 * This extracts all routes from the navigation groups and validates them
 */

// Import the navigation groups structure from the actual component
const navigationGroups = [
  {
    id: "core",
    label: "Core",
    items: [
      {
        href: "/dashboard",
        label: "Dashboard"
      }
    ]
  },
  {
    id: "people",
    label: "People Management",
    items: [
      {
        href: "/dashboard/employees",
        label: "Employees"
      },
      {
        href: "/dashboard/add-people",
        label: "Add People"
      },
      {
        href: "/dashboard/team",
        label: "Team"
      },
      {
        href: "/dashboard/departments",
        label: "Departments"
      }
    ]
  },
  {
    id: "appraisals",
    label: "Appraisal System",
    items: [
      {
        href: "/dashboard/approvals",
        label: "Approvals"
      },
      {
        href: "/dashboard/approvals/multi-level",
        label: "Multi-Level Approvals"
      },
      {
        href: "/dashboard/periods",
        label: "Appraisal Periods"
      }
    ]
  },
  {
    id: "financial",
    label: "Financial",
    items: [
      {
        href: "/dashboard/accounting",
        label: "Accounting"
      }
    ]
  },
  {
    id: "time-benefits",
    label: "Time & Benefits",
    items: [
      {
        href: "/dashboard/pto",
        label: "PTO"
      }
    ]
  },
  {
    id: "communication",
    label: "Communication",
    items: [
      {
        href: "/dashboard/feedback",
        label: "Feedback"
      },
      {
        href: "/dashboard/hr/feedback",
        label: "HR Feedback"
      }
    ]
  },
  {
    id: "administration",
    label: "Administration",
    items: [
      {
        href: "/dashboard/admin",
        label: "System Admin"
      },
      {
        href: "/dashboard/admin/email-settings",
        label: "Email Settings"
      }
    ]
  }
]

// Extract all routes from navigation groups
function extractAllRoutes() {
  const routes = []
  
  navigationGroups.forEach(group => {
    group.items.forEach(item => {
      routes.push({
        href: item.href,
        label: item.label,
        group: group.label
      })
    })
  })
  
  return routes
}

// Validate route structure
function validateRoutes(routes) {
  console.log('🧪 NAVIGATION ROUTES VALIDATION\n')
  
  const issues = []
  const routeMap = new Map()
  
  routes.forEach(route => {
    // Check for duplicate routes
    if (routeMap.has(route.href)) {
      issues.push(`❌ Duplicate route found: ${route.href} (${route.label} and ${routeMap.get(route.href).label})`)
    } else {
      routeMap.set(route.href, route)
    }
    
    // Check route format
    if (!route.href.startsWith('/dashboard')) {
      issues.push(`❌ Invalid route format: ${route.href} (should start with /dashboard)`)
    }
    
    // Check for empty labels
    if (!route.label || route.label.trim() === '') {
      issues.push(`❌ Empty label for route: ${route.href}`)
    }
  })
  
  return { issues, routeMap }
}

// Check for missing routes (routes that existed before restructure)
function checkMissingRoutes(currentRoutes) {
  const expectedRoutes = [
    '/dashboard',
    '/dashboard/approvals',
    '/dashboard/approvals/multi-level',
    '/dashboard/accounting',
    '/dashboard/employees',
    '/dashboard/add-people',
    '/dashboard/team',
    '/dashboard/departments',
    '/dashboard/periods',
    '/dashboard/pto',
    '/dashboard/feedback',
    '/dashboard/hr/feedback',
    '/dashboard/admin',
    '/dashboard/admin/email-settings'
  ]
  
  const currentHrefs = currentRoutes.map(r => r.href)
  const missingRoutes = expectedRoutes.filter(route => !currentHrefs.includes(route))
  
  return missingRoutes
}

// Check route organization
function validateRouteOrganization(routes) {
  console.log('📁 ROUTE ORGANIZATION BY GROUP\n')
  
  const groupedRoutes = {}
  
  routes.forEach(route => {
    if (!groupedRoutes[route.group]) {
      groupedRoutes[route.group] = []
    }
    groupedRoutes[route.group].push(route)
  })
  
  Object.keys(groupedRoutes).forEach(groupName => {
    console.log(`📂 ${groupName}:`)
    groupedRoutes[groupName].forEach(route => {
      console.log(`   🔗 ${route.href} → ${route.label}`)
    })
    console.log('')
  })
  
  return groupedRoutes
}

// Check for logical grouping issues
function validateLogicalGrouping(groupedRoutes) {
  console.log('🔍 LOGICAL GROUPING VALIDATION\n')
  
  const issues = []
  
  // Check if related routes are in the same group
  const relatedRoutes = [
    ['/dashboard/approvals', '/dashboard/approvals/multi-level'],
    ['/dashboard/feedback', '/dashboard/hr/feedback'],
    ['/dashboard/admin', '/dashboard/admin/email-settings']
  ]
  
  relatedRoutes.forEach(([route1, route2]) => {
    const group1 = Object.keys(groupedRoutes).find(group => 
      groupedRoutes[group].some(r => r.href === route1)
    )
    const group2 = Object.keys(groupedRoutes).find(group => 
      groupedRoutes[group].some(r => r.href === route2)
    )
    
    if (group1 && group2 && group1 !== group2) {
      issues.push(`⚠️  Related routes in different groups: ${route1} (${group1}) and ${route2} (${group2})`)
    } else if (group1 && group2 && group1 === group2) {
      console.log(`✅ Related routes properly grouped: ${route1} and ${route2} in ${group1}`)
    }
  })
  
  return issues
}

// Run all tests
console.log('🚀 STARTING NAVIGATION ROUTES VALIDATION\n')

const allRoutes = extractAllRoutes()
console.log(`📊 Total routes found: ${allRoutes.length}\n`)

// Validate basic route structure
const { issues, routeMap } = validateRoutes(allRoutes)

if (issues.length === 0) {
  console.log('✅ All routes have valid structure\n')
} else {
  console.log('❌ Route structure issues found:')
  issues.forEach(issue => console.log(`   ${issue}`))
  console.log('')
}

// Check for missing routes
const missingRoutes = checkMissingRoutes(allRoutes)
if (missingRoutes.length === 0) {
  console.log('✅ All expected routes are present\n')
} else {
  console.log('❌ Missing routes:')
  missingRoutes.forEach(route => console.log(`   ${route}`))
  console.log('')
}

// Validate route organization
const groupedRoutes = validateRouteOrganization(allRoutes)

// Check logical grouping
const groupingIssues = validateLogicalGrouping(groupedRoutes)
if (groupingIssues.length === 0) {
  console.log('✅ All related routes are properly grouped\n')
} else {
  console.log('⚠️  Grouping issues found:')
  groupingIssues.forEach(issue => console.log(`   ${issue}`))
  console.log('')
}

// Summary
console.log('📋 VALIDATION SUMMARY')
console.log('='.repeat(50))
console.log(`Total routes: ${allRoutes.length}`)
console.log(`Groups: ${Object.keys(groupedRoutes).length}`)
console.log(`Structure issues: ${issues.length}`)
console.log(`Missing routes: ${missingRoutes.length}`)
console.log(`Grouping issues: ${groupingIssues.length}`)

const totalIssues = issues.length + missingRoutes.length + groupingIssues.length
if (totalIssues === 0) {
  console.log('\n🎉 ALL TESTS PASSED! Navigation restructure is successful.')
} else {
  console.log(`\n⚠️  ${totalIssues} issues found. Please review and fix.`)
}

console.log('\n🏁 Validation completed!')
