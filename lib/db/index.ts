// Barrel exports for all database modules
export * from './core/health'
export * from './core/types'
export * from './core/utils'

// Domain exports
export * from './domains/departments'
export * from './domains/managers'
export * from './domains/user-roles'
export * from './domains/employees'
export * from './domains/periods'
export * from './domains/templates'
export * from './domains/pto'
export * from './domains/relationships'
export * from './domains/feedback'
export * from './domains/appraisals'
export * from './domains/revisions'

// Import all functions for the legacy db object
import { healthCheck } from './core/health'
import { query } from './core/utils'
import { 
  getDepartments, 
  createDepartment, 
  updateDepartment, 
  deleteDepartment 
} from './domains/departments'
import { 
  getManagers, 
  getManagerByClerkId, 
  createManager, 
  ensureManagerExists 
} from './domains/managers'
import { 
  getUserRole, 
  createUserRole, 
  updateUserRole 
} from './domains/user-roles'
import { 
  getEmployees, 
  getEmployee<PERSON>yId, 
  getEmployeeByClerkId, 
  createEmployee, 
  updateEmployee, 
  softDeleteEmployee 
} from './domains/employees'
import { 
  getAppraisalPeriods, 
  createAppraisalPeriod, 
  updateAppraisalPeriod 
} from './domains/periods'
import { 
  getTemplates, 
  getTemplateById, 
  getDefaultTemplate, 
  createTemplate, 
  updateTemplate, 
  deleteTemplate, 
  duplicateTemplate, 
  trackTemplateUsage, 
  getTemplateUsageStats 
} from './domains/templates'
import { 
  getPTOBalance, 
  createPTOBalance, 
  updatePTOBalance, 
  createPTORequest, 
  getPTORequests, 
  getPTORequestById, 
  approvePTORequest, 
  rejectPTORequest, 
  cancelPTORequest, 
  getPTOStats, 
  checkPTOAvailability, 
  initializePTOBalances 
} from './domains/pto'
import { 
  getEmployeeManagers, 
  assignManagerToEmployee, 
  removeManagerFromEmployee, 
  getAppraisalCompletionStatus 
} from './domains/relationships'
import { 
  createFeedback, 
  getFeedback, 
  getFeedbackById, 
  updateFeedbackStatus, 
  addFeedbackComment, 
  getFeedbackComments, 
  getFeedbackStatistics, 
  getPendingFeedbackForHR 
} from './domains/feedback'
import { 
  getAppraisalByEmployeeId, 
  saveAppraisalDraft, 
  submitAppraisal, 
  getPerformanceStatsByPeriod, 
  getAllAppraisalsForManager, 
  getAppraisalsWithEmployeeData, 
  getSubmittedAppraisals, 
  updateAppraisalStatus 
} from './domains/appraisals'
import { 
  createAppraisalRevision, 
  updateAppraisalRevision, 
  resubmitAppraisalRevision 
} from './domains/revisions'

// Legacy db object for backward compatibility
// This maintains the existing API while using the new modular structure
export const db = {
  // Core utilities (keep original signatures)
  healthCheck,
  query,

  // Departments
  getDepartments,
  createDepartment,
  updateDepartment,
  deleteDepartment,

  // Managers
  getManagers,
  getManagerByClerkId,
  createManager,
  ensureManagerExists,

  // User Roles
  getUserRole,
  createUserRole,
  updateUserRole,

  // Employees
  getEmployees,
  getEmployeeById,
  getEmployeeByClerkId,
  createEmployee,
  updateEmployee,
  softDeleteEmployee,

  // Appraisal Periods
  getAppraisalPeriods,
  createAppraisalPeriod,
  updateAppraisalPeriod,

  // Templates
  getTemplates,
  getTemplateById,
  getDefaultTemplate,
  createTemplate,
  updateTemplate,
  deleteTemplate,
  duplicateTemplate,
  trackTemplateUsage,
  getTemplateUsageStats,

  // PTO
  getPTOBalance,
  createPTOBalance,
  updatePTOBalance,
  createPTORequest,
  getPTORequests,
  getPTORequestById,
  approvePTORequest,
  rejectPTORequest,
  cancelPTORequest,
  getPTOStats,
  checkPTOAvailability,
  initializePTOBalances,

  // Employee-Manager Relationships
  getEmployeeManagers,
  assignManagerToEmployee,
  removeManagerFromEmployee,
  getAppraisalCompletionStatus,

  // Feedback
  createFeedback,
  getFeedback,
  getFeedbackById,
  updateFeedbackStatus,
  addFeedbackComment,
  getFeedbackComments,
  getFeedbackStatistics,
  getPendingFeedbackForHR,

  // Appraisals
  getAppraisalByEmployeeId,
  saveAppraisalDraft,
  submitAppraisal,
  getPerformanceStatsByPeriod,
  getAllAppraisalsForManager,
  getAppraisalsWithEmployeeData,
  getSubmittedAppraisals,
  updateAppraisalStatus,

  // Revisions
  createAppraisalRevision,
  updateAppraisalRevision,
  resubmitAppraisalRevision
}