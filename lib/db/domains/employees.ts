import { supabaseAdmin } from '../../supabase'
import { debug } from '../../debug'
import type { Employee, EmployeeWithDepartment } from '../core/types'

// Employees
export async function getEmployees(): Promise<EmployeeWithDepartment[]> {
  debug.log('🔍 [DEBUG] Starting getEmployees() function')

  // Fetch employees with departments only (no manager join due to missing FK constraint)
  const { data: employees, error: employeesError } = await supabaseAdmin.from('appy_employees')
    .select(`
      *,
      appy_departments:department_id (
        id,
        name
      )
    `)
    .eq('active', true)
    .order('full_name')

  if (employeesError) {
    throw new Error(employeesError.message)
  }

  debug.log('👥 [DEBUG] Fetched employees:', employees?.map(emp => ({
    id: emp.id,
    full_name: emp.full_name,
    manager_id: emp.manager_id
  })))

  // Fetch all managers separately
  const { data: managers, error: managersError } = await supabaseAdmin.from('appy_managers')
    .select('user_id, full_name')
    .eq('active', true)

  if (managersError) {
    throw new Error(managersError.message)
  }

  debug.log('👨‍💼 [DEBUG] Fetched managers:', managers?.map(mgr => ({
    user_id: mgr.user_id,
    full_name: mgr.full_name
  })))

  // Create a lookup map for managers
  const managerMap = new Map(
    (managers || []).map(manager => [manager.user_id, manager.full_name])
  )

  debug.log('🗺️ [DEBUG] Manager lookup map:', Array.from(managerMap.entries()))

  // Transform the data to match expected format
  const result = (employees || []).map(employee => {
    const managerName = employee.manager_id ? managerMap.get(employee.manager_id) || null : null

    debug.log(`🔗 [DEBUG] Employee ${employee.full_name}: manager_id="${employee.manager_id}" -> manager_name="${managerName}"`)

    return {
      ...employee,
      department_name: employee.appy_departments?.name || '',
      manager_name: managerName
    }
  })

  debug.log('✅ [DEBUG] Final employee data with managers:', result.map(emp => ({
    full_name: emp.full_name,
    manager_id: emp.manager_id,
    manager_name: emp.manager_name
  })))

  return result
}

export async function getEmployeeById(id: string): Promise<EmployeeWithDepartment | null> {
  // Fetch employee with department only (no manager join due to missing FK constraint)
  const { data: employee, error: employeeError } = await supabaseAdmin.from('appy_employees')
    .select(`
      *,
      appy_departments:department_id (
        id,
        name
      )
    `)
    .eq('id', id)
    .single()
  
  if (employeeError) {
    if (employeeError.code === 'PGRST116') {
      return null // Not found
    }
    throw new Error(employeeError.message)
  }

  // Fetch manager separately if employee has one
  let managerName = null
  if (employee.manager_id) {
    const { data: manager, error: managerError } = await supabaseAdmin.from('appy_managers')
      .select('full_name')
      .eq('user_id', employee.manager_id)
      .eq('active', true)
      .single()
    
    if (!managerError && manager) {
      managerName = manager.full_name
    }
  }
  
  return {
    ...employee,
    department_name: employee.appy_departments?.name || '',
    manager_name: managerName
  }
}

export async function getEmployeeByClerkId(clerkId: string): Promise<EmployeeWithDepartment | null> {
  debug.log('🔍 Getting employee by Clerk ID:', clerkId)

  // First try to find by manager_id (if they are a manager)
  const { data: managerEmployee, error: managerError } = await supabaseAdmin.from('appy_employees')
    .select(`
      *,
      appy_departments:department_id (
        id,
        name
      )
    `)
    .eq('manager_id', clerkId)
    .single()

  if (managerEmployee && !managerError) {
    debug.log('✅ Found employee by manager_id:', managerEmployee.full_name)
    return {
      ...managerEmployee,
      departmentName: managerEmployee.appy_departments?.name || null
    }
  }

  // If not found as manager, try to find by email through managers table
  const { data: manager, error: managerLookupError } = await supabaseAdmin.from('appy_managers')
    .select('email')
    .eq('user_id', clerkId)
    .single()

  if (manager && !managerLookupError) {
    const { data: employeeByEmail, error: emailError } = await supabaseAdmin.from('appy_employees')
      .select(`
        *,
        appy_departments:department_id (
          id,
          name
        )
      `)
      .eq('email', manager.email)
      .single()

    if (employeeByEmail && !emailError) {
      debug.log('✅ Found employee by email:', employeeByEmail.full_name)
      return {
        ...employeeByEmail,
        departmentName: employeeByEmail.appy_departments?.name || null
      }
    }
  }

  debug.log('❌ Employee not found for Clerk ID:', clerkId)
  return null
}

export async function createEmployee(employeeData: {
  fullName: string
  email: string
  compensation: number
  rate: 'hourly' | 'monthly'
  departmentId: string
  managerId?: string
}): Promise<Employee> {
  const { data, error } = await supabaseAdmin.from('appy_employees')
    .insert({
      full_name: employeeData.fullName,
      email: employeeData.email,
      compensation: employeeData.compensation,
      rate: employeeData.rate,
      department_id: employeeData.departmentId,
      manager_id: employeeData.managerId || null
    })
    .select()
    .single()
  
  if (error) {
    throw new Error(error.message)
  }
  
  return data
}

export async function updateEmployee(id: string, employeeData: {
  fullName?: string
  email?: string
  compensation?: number
  rate?: 'hourly' | 'monthly'
  departmentId?: string
  managerId?: string
  active?: boolean
}): Promise<Employee> {
  const updateData: any = {}
  
  if (employeeData.fullName !== undefined) updateData.full_name = employeeData.fullName
  if (employeeData.email !== undefined) updateData.email = employeeData.email
  if (employeeData.compensation !== undefined) updateData.compensation = employeeData.compensation
  if (employeeData.rate !== undefined) updateData.rate = employeeData.rate
  if (employeeData.departmentId !== undefined) updateData.department_id = employeeData.departmentId
  if (employeeData.managerId !== undefined) updateData.manager_id = employeeData.managerId
  if (employeeData.active !== undefined) updateData.active = employeeData.active
  
  if (Object.keys(updateData).length === 0) {
    throw new Error('No fields to update')
  }
  
  const { data, error } = await supabaseAdmin.from('appy_employees')
    .update(updateData)
    .eq('id', id)
    .select()
    .single()
  
  if (error) {
    throw new Error(error.message)
  }
  
  return data
}

export async function softDeleteEmployee(id: string): Promise<void> {
  const { error } = await supabaseAdmin.from('appy_employees')
    .update({ active: false })
    .eq('id', id)
  
  if (error) {
    throw new Error(error.message)
  }
}